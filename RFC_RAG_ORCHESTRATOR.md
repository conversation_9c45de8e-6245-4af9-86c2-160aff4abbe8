# RFC: RAG Orchestrator - A Lightweight, Composable RAG Framework

**RFC Number:** 001  
**Title:** RAG Orchestrator Design and Architecture  
**Author:** [Your Name]  
**Status:** Draft  
**Created:** 2025-01-09  
**Updated:** 2025-01-09  

## Summary

This RFC proposes the design and architecture for `rag-orchestrator`, a lightweight, composable Python library for Retrieval-Augmented Generation (RAG) applications. The library aims to fill the gap between simple tutorials and enterprise-complexity frameworks by providing a clean, performant, and well-tested foundation for RAG systems.

## Motivation

### Current State of RAG Frameworks

The Python RAG ecosystem currently suffers from several issues:

1. **Complexity vs. Simplicity Trade-off**: Existing frameworks are either too simple (basic tutorials) or overly complex (enterprise solutions like LangChain/Haystack)
2. **Poor Testing Culture**: Most frameworks lack comprehensive test coverage and testing utilities
3. **Performance Overhead**: Heavy abstraction layers impact response times
4. **Developer Experience**: Complex APIs, poor error messages, and difficult debugging

### Goals

- **Lightweight**: Minimal dependencies, fast startup times
- **Composable**: Mix-and-match components with clear interfaces
- **Well-tested**: >90% test coverage with comprehensive test utilities
- **Performance-first**: <100ms average response time
- **Developer-friendly**: Intuitive APIs with excellent type hints

## Detailed Design

### Core Architecture

The library follows a three-layer architecture:

```
┌─────────────────┐
│   Orchestrator  │  ← Main coordination layer
├─────────────────┤
│   Retrievers    │  ← Document retrieval layer
├─────────────────┤
│   Generators    │  ← Response generation layer
└─────────────────┘
```

### API Design

#### 1. Core Orchestrator

```python
class Orchestrator:
    def __init__(
        self,
        retriever: BaseRetriever,
        generator: BaseGenerator,
        *,
        max_context_length: int = 4000,
        temperature: float = 0.1,
        enable_caching: bool = True,
        enable_metrics: bool = True
    ) -> None: ...
    
    async def query(
        self,
        query: str,
        *,
        top_k: int = 5,
        filters: Optional[Dict[str, Any]] = None,
        stream: bool = False
    ) -> RAGResponse: ...
```

**Design Questions:**
1. Should the orchestrator support multiple retrievers/generators simultaneously?
2. How should we handle retriever-generator compatibility validation?
3. Should caching be at the orchestrator level or component level?

#### 2. Retriever Interface

```python
class BaseRetriever(ABC):
    @abstractmethod
    async def retrieve(
        self,
        query: str,
        top_k: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Document]: ...
    
    @abstractmethod
    async def add_documents(self, documents: List[Document]) -> None: ...
```

**Design Questions:**
1. Should retrievers handle their own embedding models or receive embeddings?
2. How should we standardize metadata filtering across different retriever types?
3. Should we support batch retrieval operations?

#### 3. Generator Interface

```python
class BaseGenerator(ABC):
    @abstractmethod
    async def generate(
        self,
        query: str,
        context: List[Document],
        *,
        temperature: float = 0.1,
        max_tokens: int = 500,
        stream: bool = False
    ) -> Union[GenerationResponse, AsyncIterator[str]]: ...
```

**Design Questions:**
1. How should we handle context length limits across different models?
2. Should generators be responsible for prompt engineering or should that be configurable?
3. How do we handle streaming vs. non-streaming responses uniformly?

### Data Models

```python
@dataclass
class Document:
    id: str
    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    embedding: Optional[List[float]] = None

@dataclass
class RAGResponse:
    answer: str
    sources: List[Document]
    metadata: ResponseMetadata
```

**Design Questions:**
1. Should `Document` support different content types (text, images, tables)?
2. How should we handle document versioning and updates?
3. Should embeddings be stored with documents or separately?

## Implementation Strategy

### Phase 1: Core Foundation (Weeks 1-2)
- Basic orchestrator implementation
- Vector retriever with sentence-transformers
- OpenAI generator integration
- Core data models and interfaces

### Phase 2: Enhanced Features (Weeks 3-4)
- BM25 and hybrid retrieval
- Additional generator backends (Anthropic, HuggingFace)
- Caching and metrics systems
- Comprehensive test suite

### Phase 3: Production Ready (Weeks 5-6)
- Performance optimization
- Documentation and examples
- CI/CD pipeline
- Community feedback integration

## Open Questions

### 1. Dependency Management
**Question**: How minimal should our dependencies be?

**Options**:
- A) Ultra-minimal: Only `pydantic`, `httpx`, `tenacity`
- B) Practical: Include `sentence-transformers` for default vector retrieval
- C) Batteries-included: Include common ML libraries

**Recommendation**: Option A with optional dependencies for specific features.

### 2. Configuration System
**Question**: How should users configure the library?

**Options**:
- A) Pure Python configuration (current design)
- B) YAML/JSON configuration files
- C) Environment variable support
- D) All of the above

**Recommendation**: Start with A, add B and C based on community feedback.

### 3. Async vs. Sync APIs
**Question**: Should we provide both async and sync APIs?

**Options**:
- A) Async-only (current design)
- B) Sync-only for simplicity
- C) Both with sync wrapping async

**Recommendation**: Option A for performance, with sync wrappers if needed.

### 4. Error Handling Strategy
**Question**: How should we handle errors across the pipeline?

**Options**:
- A) Fail fast with detailed error messages
- B) Graceful degradation with fallbacks
- C) Configurable error handling strategies

**Recommendation**: Option A with optional fallback mechanisms.

### 5. Extensibility Mechanism
**Question**: How should users extend the library?

**Options**:
- A) Inheritance-based (current design)
- B) Plugin system with registration
- C) Composition-based with protocols

**Recommendation**: Start with A, consider B for complex extensions.

## Performance Considerations

### Latency Targets
- Cold start: <500ms
- Warm queries: <100ms average
- Retrieval: <50ms for 10k documents
- Generation: <2s for 500 tokens

### Memory Usage
- Base library: <50MB
- With embeddings: <500MB for 10k documents
- Configurable caching limits

### Scalability
- Support 100k+ documents
- Handle 100+ concurrent requests
- Horizontal scaling through stateless design

## Testing Strategy

### Test Coverage Requirements
- Unit tests: >95% coverage
- Integration tests: End-to-end workflows
- Performance tests: Latency benchmarks
- Property-based tests: Edge case discovery

### Test Utilities
```python
# Example test utilities we'll provide
from rag_orchestrator.testing import MockRetriever, MockGenerator, RAGTestCase

class TestMyRAGApp(RAGTestCase):
    def test_basic_query(self):
        orchestrator = self.create_test_orchestrator()
        response = await orchestrator.query("test query")
        self.assert_valid_response(response)
```

## Community Feedback Requested

We're seeking feedback on the following areas:

### 1. API Design
- Is the three-layer architecture intuitive?
- Are the method signatures clear and consistent?
- What additional methods or parameters would be useful?

### 2. Use Case Coverage
- Does this design support your RAG use cases?
- What specific retrievers/generators would you need?
- Are there missing abstractions or interfaces?

### 3. Performance Requirements
- Are the latency targets realistic for your applications?
- What scalability requirements are we missing?
- How important is memory efficiency vs. performance?

### 4. Developer Experience
- Is the API intuitive for new users?
- What documentation or examples would be most helpful?
- How can we make debugging and troubleshooting easier?

### 5. Ecosystem Integration
- What existing tools should we integrate with?
- How important is compatibility with LangChain/LlamaIndex?
- What deployment scenarios should we optimize for?

## Next Steps

1. **Community Review Period**: 2 weeks for feedback and discussion
2. **Design Refinement**: Incorporate feedback into final design
3. **Implementation**: Begin development based on finalized RFC
4. **Alpha Release**: Early version for community testing
5. **Iteration**: Refine based on real-world usage

## How to Provide Feedback

Please provide feedback through:
- **GitHub Discussions**: [Link to be added]
- **Issues**: For specific technical concerns
- **Email**: [Your email] for private feedback
- **Community Channels**: [Discord/Slack links if applicable]

## References

- [Market Analysis Document](research/market_analysis.md)
- [Project Specification](PROJECT_SPECIFICATION.md)
- [LangChain Documentation](https://python.langchain.com/)
- [LlamaIndex Documentation](https://docs.llamaindex.ai/)
- [Haystack Documentation](https://haystack.deepset.ai/)

---

**Thank you for your time and feedback! Your input will help shape the future of RAG development in Python.**
